'use client';

import React from 'react';
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>ltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { format } from 'date-fns';
import { FormattedLogEntry } from '@/lib/types/log';

interface ProcessingTimeChartProps {
  data: FormattedLogEntry[];
  dateRange: {
    from: Date;
    to: Date;
  };
}

// Helper function to calculate percentiles
const calculatePercentile = (values: number[], percentile: number) => {
  if (values.length === 0) return 0;
  const sorted = [...values].sort((a, b) => a - b);
  const index = Math.ceil((percentile / 100) * sorted.length) - 1;
  return sorted[index];
};

export function ProcessingTimeChart({ data, dateRange }: ProcessingTimeChartProps) {
  // Process data to calculate latency metrics
  const chartData = React.useMemo(() => {
    // Filter logs that are completed and within the date range
    const completedLogs = data.filter(log => {
      if (!log.processedAt || !log.requestTimestamp) return false;
      const date = new Date(log.requestTimestamp);
      return date >= dateRange.from && date <= dateRange.to;
    });

    // If we have very few completed logs, show a message instead of an empty chart
    if (completedLogs.length === 0) {
      return [];
    }



    // Create an array of all days in the date range
    const allDays: string[] = [];
    let currentDate = new Date(dateRange.from);
    currentDate.setHours(0, 0, 0, 0); // Ensure we start at beginning of day

    const endDate = new Date(dateRange.to);
    endDate.setHours(23, 59, 59, 999); // Include full last day

    while (currentDate <= endDate) {
      allDays.push(currentDate.toISOString().split('T')[0]);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Group logs by timestamp (daily)
    const groupedData = completedLogs
      .reduce((acc: { [key: string]: number[] }, log) => {
        // Parse timestamps
        const requestTime = new Date(log.requestTimestamp);
        const processedTime = new Date(log.processedAt!);

        // Calculate processing time in seconds
        const processingTimeSec = (processedTime.getTime() - requestTime.getTime()) / 1000;

        // Group by day (using YYYY-MM-DD format as key)
        const key = requestTime.toISOString().split('T')[0];

        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(processingTimeSec);

        return acc;
      }, {});

    // Calculate percentiles for each day, only include days with data
    const result = allDays
      .map(day => ({
        timestamp: day + 'T00:00:00.000Z',
        p50: groupedData[day] ? calculatePercentile(groupedData[day], 50) : null,
        p95: groupedData[day] ? calculatePercentile(groupedData[day], 95) : null,
        p99: groupedData[day] ? calculatePercentile(groupedData[day], 99) : null,
        hasData: !!groupedData[day]
      }))
      .filter(item => item.hasData); // Only include days with actual data



    return result;
  }, [data, dateRange]);

  // Group data by date and request type for the median latency chart
  const medianLatencyData = React.useMemo(() => {
    // Filter completed logs within date range
    const completedLogs = data.filter(log => {
      if (!log.processedAt || !log.requestTimestamp) return false;
      const date = new Date(log.requestTimestamp);
      return date >= dateRange.from && date <= dateRange.to;
    });

    if (completedLogs.length === 0) {
      return [];
    }

    // Group logs by date and request type
    const groupedData = completedLogs.reduce((acc: { [key: string]: { [requestType: string]: number[] } }, log) => {
      const requestTime = new Date(log.requestTimestamp);
      const processedTime = new Date(log.processedAt!);
      const processingTimeSec = (processedTime.getTime() - requestTime.getTime()) / 1000;

      // Group by day (using YYYY-MM-DD format as key)
      const dateKey = requestTime.toISOString().split('T')[0];

      if (!acc[dateKey]) {
        acc[dateKey] = {};
      }
      if (!acc[dateKey][log.requestType]) {
        acc[dateKey][log.requestType] = [];
      }
      acc[dateKey][log.requestType].push(processingTimeSec);

      return acc;
    }, {});

    // Calculate median for each date-requestType combination
    const chartData = Object.entries(groupedData)
      .map(([dateKey, requestTypes]) => {
        const dataPoint: any = {
          timestamp: dateKey,
          date: new Date(dateKey)
        };

        // Calculate median for each request type on this date
        Object.entries(requestTypes).forEach(([requestType, times]) => {
          const median = calculatePercentile(times, 50);
          if (requestType === 'invoice') {
            dataPoint.standardInvoice = median;
          } else if (requestType === 'taxInvoice') {
            dataPoint.taxInvoice = median;
          }
        });

        return dataPoint;
      })
      .sort((a, b) => a.date.getTime() - b.date.getTime());

    return chartData;
  }, [data, dateRange]);

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium">Overall Processing Time</CardTitle>
        </CardHeader>
        <CardContent>
          {chartData.length === 0 ? (
            <div className="flex items-center justify-center h-[300px] text-muted-foreground">
              <div className="text-center">
                <p className="text-sm">No processing data available for the selected date range.</p>
                <p className="text-xs mt-1">Try selecting a different time period or check if there are completed processing logs.</p>
              </div>
            </div>
          ) : chartData.length === 1 ? (
            <div className="space-y-4">
              <div className="flex items-center justify-center h-[60px] text-muted-foreground bg-muted/30 rounded-md">
                <div className="text-center">
                  <p className="text-sm">Only one day of data available in the selected range.</p>
                  <p className="text-xs mt-1">Try selecting "Last Month" or a custom date range to see trends over time.</p>
                </div>
              </div>
              <ResponsiveContainer width="100%" height={240}>
                <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="timestamp"
                    tickFormatter={timestamp => format(new Date(timestamp), 'MMM d')}
                    interval="preserveStartEnd"
                  />
                  <YAxis
                    tickFormatter={value => value.toFixed(1)}
                    domain={[0, 24]}
                    label={{ value: 'seconds', angle: -90, position: 'insideLeft' }}
                  />
                  <Tooltip
                    labelFormatter={timestamp => format(new Date(timestamp), 'MMM d')}
                    formatter={(value: number, name) => [`${value.toFixed(1)} sec`, name]}
                    contentStyle={{ background: '#fff', border: '1px solid #ccc', borderRadius: '4px', padding: '8px' }}
                  />
                  <Legend verticalAlign="top" height={36}/>
                  <Line
                    type="linear"
                    dataKey="p50"
                    stroke="#2563eb"
                    name="p50"
                    strokeWidth={2}
                    dot={{ r: 4, fill: '#2563eb' }}
                    isAnimationActive={false}
                    connectNulls={true}
                  />
                  <Line
                    type="linear"
                    dataKey="p95"
                    stroke="#16a34a"
                    name="p95"
                    strokeWidth={2}
                    dot={{ r: 4, fill: '#16a34a' }}
                    isAnimationActive={false}
                    connectNulls={true}
                  />
                  <Line
                    type="linear"
                    dataKey="p99"
                    stroke="#dc2626"
                    name="p99"
                    strokeWidth={2}
                    dot={{ r: 4, fill: '#dc2626' }}
                    isAnimationActive={false}
                    connectNulls={true}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="timestamp"
                  tickFormatter={timestamp => format(new Date(timestamp), 'MMM d')}
                  interval="preserveStartEnd"
                />
                <YAxis
                  tickFormatter={value => value.toFixed(1)}
                  domain={[0, 24]}
                  label={{ value: 'seconds', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip
                  labelFormatter={timestamp => format(new Date(timestamp), 'MMM d')}
                  formatter={(value: number, name) => [`${value.toFixed(1)} sec`, name]}
                  contentStyle={{ background: '#fff', border: '1px solid #ccc', borderRadius: '4px', padding: '8px' }}
                />
                <Legend verticalAlign="top" height={36}/>
                <Line
                  type="linear"
                  dataKey="p50"
                  stroke="#2563eb"
                  name="p50"
                  strokeWidth={2}
                  dot={{ r: 4, fill: '#2563eb' }}
                  isAnimationActive={false}
                  connectNulls={true}
                />
                <Line
                  type="linear"
                  dataKey="p95"
                  stroke="#16a34a"
                  name="p95"
                  strokeWidth={2}
                  dot={{ r: 4, fill: '#16a34a' }}
                  isAnimationActive={false}
                  connectNulls={true}
                />
                <Line
                  type="linear"
                  dataKey="p99"
                  stroke="#dc2626"
                  name="p99"
                  strokeWidth={2}
                  dot={{ r: 4, fill: '#dc2626' }}
                  isAnimationActive={false}
                  connectNulls={true}
                />
              </LineChart>
            </ResponsiveContainer>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium">Latency by Document Type (median)</CardTitle>
        </CardHeader>
        <CardContent>
          {medianLatencyData.length === 0 ? (
            <div className="flex items-center justify-center h-[240px] text-muted-foreground">
              <div className="text-center">
                <p className="text-sm">No completed processing logs found in the selected date range.</p>
                <p className="text-xs mt-1">Try selecting a different date range or check if there are any completed requests.</p>
              </div>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height={240}>
              <LineChart data={medianLatencyData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="timestamp"
                  tickFormatter={timestamp => format(new Date(timestamp), 'MMM d')}
                  interval="preserveStartEnd"
                />
                <YAxis
                  tickFormatter={value => value.toFixed(1)}
                  domain={[0, 'auto']}
                  label={{ value: 'seconds', angle: -90, position: 'insideLeft' }}
                />
                <Tooltip
                  labelFormatter={timestamp => format(new Date(timestamp), 'MMM d')}
                  formatter={(value: number, name) => [`${value.toFixed(1)} sec`, name]}
                  contentStyle={{ background: '#fff', border: '1px solid #ccc', borderRadius: '4px', padding: '8px' }}
                />
                <Legend verticalAlign="top" height={36}/>
                <Line
                  type="linear"
                  dataKey="standardInvoice"
                  stroke="#2563eb"
                  name="Standard Invoice"
                  strokeWidth={2}
                  dot={{ r: 4, fill: '#2563eb' }}
                  isAnimationActive={false}
                  connectNulls={false}
                />
                <Line
                  type="linear"
                  dataKey="taxInvoice"
                  stroke="#16a34a"
                  name="Tax Invoice"
                  strokeWidth={2}
                  dot={{ r: 4, fill: '#16a34a' }}
                  isAnimationActive={false}
                  connectNulls={false}
                />
              </LineChart>
            </ResponsiveContainer>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
